package com.ray.springbootdemo;

import com.ray.springbootdemo.demos.web.User;
import com.ray.springbootdemo.demos.web.dao.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
@Slf4j
class SpringBootDemoApplicationTests {

    @Autowired
    private UserMapper userMapper;

    @Test
    void testQuery() {
        List<User> all = userMapper.getAll();
        log.info("查询所有：{}",all.stream().toArray());
    }
}
