<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ray.springbootdemo.demos.web.dao.UserMapper">

    <select id="getAll" resultType="User">
        select * from user
    </select>

    <select id="getOne" resultType="User">
        select * from user where id = #{id}
    </select>

    <insert id="insert">
        insert into user (name, password, age) values (#{name}, #{password}, #{age})
    </insert>

    <update id="update">
        update user set name = #{name}, password = #{password}, age = #{age} where id = #{id}
    </update>

    <delete id="delete">
        delete from user where id = #{id}
    </delete>
</mapper>