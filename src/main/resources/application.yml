server:
  port: 8080
spring:
  application:
    name: spring-boot-demo
  datasource:
    url: ********************************************************************************************************************************************************************************************************************************************
    username: demo_db
    password: liuyi123456
    driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    database: 1
    host: **************
    port: 6379
    password: 123456&Qw
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 5
        min-idle: 1
        max-idle: 3
        max-wait: 10000ms
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ray.springbootdemo.demos.web.entity