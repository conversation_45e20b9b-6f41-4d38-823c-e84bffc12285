package com.ray.springbootdemo.demos.web.dao;

import com.ray.springbootdemo.demos.web.entity.User;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *
 * <AUTHOR>
 * 创建日期： 2025-09-17
 */
@Mapper
public interface UserMapper {
    /**
     * 查询所有用户
     * @return 所有用户
     */
    List<User>  getAll();

    /**
     * 根据id查询用户
     * @param id 用户id
     * @return  用户
     */
    User getOne(Integer id);

    /**
     * 插入用户
     * @param user  用户
     */
    void insert(User user);

    /**
     * 更新用户
     * @param user  用户
     */
    void update(User user);

    /**
     * 删除用户
     * @param id 用户id
     */
    void delete(Integer id);
}
